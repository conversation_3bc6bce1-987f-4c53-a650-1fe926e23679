"""
API endpoints لإدارة بصمات الأجهزة
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import desc, select
import logging

from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory
from database.session import get_db
# from services.device_data_integration_service import DeviceDataIntegrationService  # تم إزالة هذا الملف

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/device-fingerprints", tags=["device-fingerprints"])

@router.get("/{device_id}")
async def get_device_fingerprint(device_id: str, db: Session = Depends(get_db)):
    """
    الحصول على بيانات بصمة جهاز محدد
    """
    try:
        # البحث عن البصمة في قاعدة البيانات
        stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.fingerprint_id == device_id,
            DeviceFingerprint.is_active == True
        )
        fingerprint = db.execute(stmt).scalar_one_or_none()

        if not fingerprint:
            return {
                'success': False,
                'message': 'لم يتم العثور على بصمة الجهاز',
                'fingerprint': None
            }

        # تحويل البيانات إلى dictionary
        fingerprint_data = {
            'id': fingerprint.id,
            'fingerprint_id': fingerprint.fingerprint_id,
            'hardware_fingerprint': fingerprint.hardware_fingerprint,
            'storage_fingerprint': fingerprint.storage_fingerprint,
            'screen_fingerprint': fingerprint.screen_fingerprint,
            'system_fingerprint': fingerprint.system_fingerprint,
            # تم إزالة network_fingerprint حسب متطلبات النظام الجديدة
            'fingerprint_details': fingerprint.fingerprint_details,
            'device_info': fingerprint.device_info,
            'screen_info': fingerprint.screen_info,
            'system_info': fingerprint.system_info,
            'browser_info': fingerprint.browser_info,
            'network_info': fingerprint.network_info,
            'additional_info': fingerprint.additional_info,
            'last_ip': fingerprint.last_ip,
            'last_user_agent': fingerprint.last_user_agent,
            'is_active': fingerprint.is_active,
            'auto_approved': fingerprint.auto_approved,
            'created_at': fingerprint.created_at.isoformat() if fingerprint.created_at else None,
            'updated_at': fingerprint.updated_at.isoformat() if fingerprint.updated_at else None,
            'last_seen_at': fingerprint.last_seen_at.isoformat() if fingerprint.last_seen_at else None
        }

        logger.info(f"تم جلب بيانات البصمة للجهاز: {device_id}")

        return {
            'success': True,
            'message': 'تم جلب بيانات البصمة بنجاح',
            'fingerprint': fingerprint_data
        }

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات البصمة للجهاز {device_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f'خطأ في جلب بيانات البصمة: {str(e)}')

@router.get("/{device_id}/history")
async def get_device_fingerprint_history(
    device_id: str,
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    الحصول على سجل وصول بصمة جهاز محدد
    """
    try:
        # تحديد الحد الأقصى للنتائج
        limit = min(limit, 100)  # حد أقصى 100 نتيجة

        # البحث عن البصمة أولاً
        stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.fingerprint_id == device_id,
            DeviceFingerprint.is_active == True
        )
        fingerprint = db.execute(stmt).scalar_one_or_none()

        if not fingerprint:
            return {
                'success': False,
                'message': 'لم يتم العثور على بصمة الجهاز',
                'history': []
            }

        # جلب سجل الوصول (البحث بمعرف البصمة وليس الـ ID الداخلي)
        history_stmt = select(DeviceFingerprintHistory).where(
            DeviceFingerprintHistory.fingerprint_id == device_id
        ).order_by(desc(DeviceFingerprintHistory.created_at)).offset(offset).limit(limit)

        history_records = db.execute(history_stmt).scalars().all()

        # تحويل البيانات
        history_data = []
        for record in history_records:
            history_data.append({
                'id': record.id,
                'event_type': record.event_type,
                'ip_address': record.ip_address,
                'user_agent': record.user_agent,
                'event_data': record.get_event_data(),
                'created_at': record.created_at.isoformat() if record.created_at else None
            })

        logger.info(f"تم جلب سجل البصمة للجهاز: {device_id} - {len(history_data)} سجل")

        return {
            'success': True,
            'message': 'تم جلب سجل البصمة بنجاح',
            'history': history_data,
            'pagination': {
                'limit': limit,
                'offset': offset,
                'has_more': len(history_data) == limit
            }
        }

    except Exception as e:
        logger.error(f"خطأ في جلب سجل البصمة للجهاز {device_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f'خطأ في جلب سجل البصمة: {str(e)}')

@router.get("/{device_id}/complete-details")
async def get_complete_device_details(device_id: str, db: Session = Depends(get_db)):
    """
    الحصول على تفاصيل الجهاز الكاملة مع دمج البيانات من قاعدة البيانات وملف التكوين
    """
    try:
        # البحث عن البصمة في قاعدة البيانات
        stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.fingerprint_id == device_id,
            DeviceFingerprint.is_active == True
        )
        fingerprint = db.execute(stmt).scalar_one_or_none()

        # قراءة بيانات الأجهزة المتصلة من ملف التكوين (الأجهزة المعتمدة فقط)
        import json
        from pathlib import Path

        config_file_path = Path(__file__).parent.parent / "config" / "connected_devices.json"
        connected_device_data = None

        try:
            if config_file_path.exists():
                logger.info(f"قراءة ملف التكوين: {config_file_path}")
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    connected_devices = json.load(f)

                logger.info(f"تم العثور على {len(connected_devices)} جهاز في ملف التكوين")

                # البحث عن الجهاز في ملف التكوين
                for device in connected_devices:
                    if device.get('device_id') == device_id:
                        connected_device_data = device
                        logger.info(f"✅ تم العثور على الجهاز {device_id} في ملف التكوين")
                        break

                if not connected_device_data:
                    logger.debug(f"🔍 الجهاز {device_id} غير موجود في ملف التكوين - سيتم البحث في قائمة الانتظار")
            else:
                logger.warning(f"❌ ملف التكوين غير موجود: {config_file_path}")
        except Exception as config_error:
            logger.warning(f"خطأ في قراءة ملف التكوين: {config_error}")

        # البحث في الأجهزة المعلقة إذا لم نجد الجهاز في ملف التكوين
        pending_device_data = None
        if not connected_device_data:
            try:
                from models.device_security import PendingDevice
                stmt = select(PendingDevice).where(
                    PendingDevice.device_id == device_id,
                    PendingDevice.status == "pending"
                )
                pending_device = db.execute(stmt).scalar_one_or_none()

                if pending_device:
                    pending_device_data = {
                        'device_id': pending_device.device_id,
                        'client_ip': pending_device.client_ip,
                        'hostname': pending_device.hostname,
                        'device_type': pending_device.device_type,
                        'system': pending_device.system,
                        'platform': pending_device.platform,
                        'user_agent': pending_device.user_agent,
                        'current_user': pending_device.current_user,
                        'first_access': pending_device.first_access.isoformat() if pending_device.first_access else None,
                        'last_access': pending_device.last_access.isoformat() if pending_device.last_access else None,
                        'access_count': pending_device.access_count,
                        'status': pending_device.status,
                        'requires_approval': True,
                        'is_main_server': False,
                        'is_local_access': False
                    }
                    logger.info(f"تم العثور على الجهاز في قائمة الانتظار: {device_id}")
            except Exception as pending_error:
                logger.warning(f"خطأ في البحث في الأجهزة المعلقة: {pending_error}")

        # إعداد البيانات المدمجة
        result = {
            'success': True,
            'device_id': device_id,
            'fingerprint_data': None,
            'device_config_data': connected_device_data,
            'combined_info': {},
            'data_sources': []
        }

        # إضافة بيانات البصمة إذا كانت متوفرة
        if fingerprint:
            fingerprint_data = {
                'id': fingerprint.id,
                'fingerprint_id': fingerprint.fingerprint_id,
                'hardware_fingerprint': fingerprint.hardware_fingerprint,
                'storage_fingerprint': fingerprint.storage_fingerprint,
                'screen_fingerprint': fingerprint.screen_fingerprint,
                'system_fingerprint': fingerprint.system_fingerprint,
                # تم إزالة network_fingerprint حسب متطلبات النظام الجديدة
                'fingerprint_details': fingerprint.fingerprint_details,
                'device_info': fingerprint.device_info,
                'screen_info': fingerprint.screen_info,
                'system_info': fingerprint.system_info,
                'browser_info': fingerprint.browser_info,
                'network_info': fingerprint.network_info,
                'additional_info': fingerprint.additional_info,
                'last_ip': fingerprint.last_ip,
                'last_user_agent': fingerprint.last_user_agent,
                'is_active': fingerprint.is_active,
                'auto_approved': fingerprint.auto_approved,
                'created_at': fingerprint.created_at.isoformat() if fingerprint.created_at else None,
                'updated_at': fingerprint.updated_at.isoformat() if fingerprint.updated_at else None,
                'last_seen_at': fingerprint.last_seen_at.isoformat() if fingerprint.last_seen_at else None
            }
            result['fingerprint_data'] = fingerprint_data
            result['data_sources'].append('database')

        # إضافة بيانات ملف التكوين إذا كانت متوفرة
        if connected_device_data:
            result['data_sources'].append('config_file')

        # إضافة بيانات الأجهزة المعلقة إذا كانت متوفرة
        if pending_device_data:
            result['data_sources'].append('pending_devices')

        # دمج المعلومات من كلا المصدرين
        combined_info = {}

        # معلومات أساسية من ملف التكوين (أولوية أعلى للبيانات الحديثة)
        if connected_device_data:
            combined_info.update({
                'device_id': connected_device_data.get('device_id'),
                'hostname': connected_device_data.get('hostname'),
                'platform': connected_device_data.get('platform'),
                'system': connected_device_data.get('system'),
                'machine': connected_device_data.get('machine'),
                'browser': connected_device_data.get('browser'),
                'client_ip': connected_device_data.get('client_ip'),
                'is_main_server': connected_device_data.get('is_main_server', False),
                'is_local_access': connected_device_data.get('is_local_access', False),
                'device_type': connected_device_data.get('device_type'),
                'first_access': connected_device_data.get('first_access'),
                'last_access': connected_device_data.get('last_access'),  # أولوية لملف التكوين
                'access_count': connected_device_data.get('access_count', 0),  # أولوية لملف التكوين
                'user_agent': connected_device_data.get('user_agent'),
                'current_user': connected_device_data.get('current_user'),
                'status': connected_device_data.get('status'),  # أولوية لملف التكوين
                'requires_approval': False,  # إذا كان في ملف التكوين فهو معتمد بالفعل
                'is_advanced_fingerprint': connected_device_data.get('is_advanced_fingerprint', False)
            })

            # إضافة معلومات البصمة من ملف التكوين إذا كانت متوفرة
            if connected_device_data.get('hardware_fingerprint'):
                combined_info['hardware_fingerprint'] = connected_device_data.get('hardware_fingerprint')
            if connected_device_data.get('storage_fingerprint'):
                combined_info['storage_fingerprint'] = connected_device_data.get('storage_fingerprint')
            if connected_device_data.get('screen_fingerprint'):
                combined_info['screen_fingerprint'] = connected_device_data.get('screen_fingerprint')
            if connected_device_data.get('system_fingerprint'):
                combined_info['system_fingerprint'] = connected_device_data.get('system_fingerprint')

        # إضافة بيانات الأجهزة المعلقة إذا لم نجد في ملف التكوين
        elif pending_device_data:
            # تحليل User Agent لاستخراج معلومات المتصفح
            user_agent = pending_device_data.get('user_agent', '')
            browser = 'غير معروف'

            if user_agent:
                from utils.request_utils import analyze_user_agent
                ua_analysis = analyze_user_agent(user_agent)
                browser = ua_analysis.get('browser', 'غير معروف')

                # إذا كان التحليل يعطي 'Unknown'، حاول التحليل اليدوي
                if browser == 'Unknown' or browser == 'غير معروف':
                    ua_lower = user_agent.lower()
                    if 'chrome' in ua_lower and 'edg' not in ua_lower and 'opr' not in ua_lower:
                        browser = 'Chrome'
                    elif 'firefox' in ua_lower:
                        browser = 'Firefox'
                    elif 'safari' in ua_lower and 'chrome' not in ua_lower:
                        browser = 'Safari'
                    elif 'edg' in ua_lower:
                        browser = 'Edge'
                    elif 'opr' in ua_lower or 'opera' in ua_lower:
                        browser = 'Opera'
                    elif 'samsung' in ua_lower:
                        browser = 'Samsung Browser'
                    else:
                        browser = 'غير معروف'

            combined_info.update({
                'device_id': pending_device_data.get('device_id'),
                'hostname': pending_device_data.get('hostname'),
                'platform': pending_device_data.get('platform'),
                'system': pending_device_data.get('system'),
                'browser': browser,
                'client_ip': pending_device_data.get('client_ip'),
                'is_main_server': False,
                'is_local_access': False,
                'device_type': pending_device_data.get('device_type'),
                'first_access': pending_device_data.get('first_access'),
                'last_access': pending_device_data.get('last_access'),
                'access_count': pending_device_data.get('access_count', 0),
                'user_agent': pending_device_data.get('user_agent'),
                'current_user': pending_device_data.get('current_user'),
                'status': 'pending_approval',
                'requires_approval': True,
                'is_advanced_fingerprint': fingerprint is not None  # إذا كان له بصمة في قاعدة البيانات
            })

        # إضافة معلومات البصمة المتقدمة من قاعدة البيانات
        if fingerprint:
            # تحليل المتصفح من User Agent المحفوظ في قاعدة البيانات إذا لم يكن محدداً
            if not combined_info.get('browser') or combined_info.get('browser') == 'غير معروف':
                if fingerprint.last_user_agent:
                    from utils.request_utils import analyze_user_agent
                    ua_analysis = analyze_user_agent(fingerprint.last_user_agent)
                    browser_from_db = ua_analysis.get('browser', 'غير معروف')

                    # إذا كان التحليل يعطي 'Unknown'، حاول التحليل اليدوي
                    if browser_from_db == 'Unknown':
                        ua_lower = fingerprint.last_user_agent.lower()
                        if 'chrome' in ua_lower and 'edg' not in ua_lower and 'opr' not in ua_lower:
                            browser_from_db = 'Chrome'
                        elif 'firefox' in ua_lower:
                            browser_from_db = 'Firefox'
                        elif 'safari' in ua_lower and 'chrome' not in ua_lower:
                            browser_from_db = 'Safari'
                        elif 'edg' in ua_lower:
                            browser_from_db = 'Edge'
                        elif 'opr' in ua_lower or 'opera' in ua_lower:
                            browser_from_db = 'Opera'
                        elif 'samsung' in ua_lower:
                            browser_from_db = 'Samsung Browser'
                        else:
                            browser_from_db = 'غير معروف'

                    combined_info['browser'] = browser_from_db

            # تحديث معلومات البصمة المتقدمة (إعطاء أولوية لملف التكوين)
            combined_info.update({
                'is_advanced_fingerprint': True,  # إذا كان له بصمة في قاعدة البيانات فهو متقدم
                'database_fingerprint_id': fingerprint.fingerprint_id,
                'database_created_at': fingerprint.created_at.isoformat() if fingerprint.created_at else None,
                'database_last_seen': fingerprint.last_seen_at.isoformat() if fingerprint.last_seen_at else None,
                'database_last_ip': fingerprint.last_ip,
                'is_database_active': fingerprint.is_active,
                'is_auto_approved': fingerprint.auto_approved
            })

            # إضافة بصمات من قاعدة البيانات فقط إذا لم تكن موجودة في ملف التكوين
            if not combined_info.get('hardware_fingerprint'):
                combined_info['hardware_fingerprint'] = fingerprint.hardware_fingerprint
            if not combined_info.get('storage_fingerprint'):
                combined_info['storage_fingerprint'] = fingerprint.storage_fingerprint
            if not combined_info.get('screen_fingerprint'):
                combined_info['screen_fingerprint'] = fingerprint.screen_fingerprint
            if not combined_info.get('system_fingerprint'):
                combined_info['system_fingerprint'] = fingerprint.system_fingerprint

            # ملاحظة: تم إلغاء network_fingerprint حسب المتطلبات الجديدة
            # combined_info['network_fingerprint'] = fingerprint.network_fingerprint

            # دمج معلومات إضافية من قاعدة البيانات إذا كانت متوفرة
            if fingerprint.system_info:
                try:
                    import json
                    system_info = json.loads(fingerprint.system_info)
                    if 'system' in system_info and not combined_info.get('system'):
                        combined_info['system'] = system_info['system']
                    if 'platform' in system_info and not combined_info.get('platform'):
                        combined_info['platform'] = system_info['platform']
                except:
                    pass

            if fingerprint.browser_info:
                try:
                    import json
                    browser_info = json.loads(fingerprint.browser_info)
                    if 'browser' in browser_info and (not combined_info.get('browser') or combined_info.get('browser') == 'غير معروف'):
                        combined_info['browser'] = browser_info['browser']
                except:
                    pass

        result['combined_info'] = combined_info
        result['message'] = f'تم جلب تفاصيل الجهاز من {len(result["data_sources"])} مصدر'

        logger.info(f"تم جلب التفاصيل الكاملة للجهاز: {device_id} من المصادر: {result['data_sources']}")

        return result

    except Exception as e:
        logger.error(f"خطأ في جلب التفاصيل الكاملة للجهاز {device_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f'خطأ في جلب تفاصيل الجهاز: {str(e)}')


@router.get("/{device_id}/integrated-data")
async def get_integrated_device_data(
    device_id: str,
    db: Session = Depends(get_db)
):
    """
    الحصول على البيانات المدمجة للجهاز (حية + ثابتة)

    البيانات الحية من ملف التكوين:
    - حالة النشاط الفعلية
    - أول وصول وآخر وصول
    - مدة الجلسة ووقت الجلسة
    - المستخدم الحالي
    - عدد مرات الوصول

    البيانات الثابتة من قاعدة البيانات:
    - بصمة الجهاز
    - سجلات الوصول
    - تفاصيل البصمة
    - معلومات الجهاز
    """
    try:
        logger.info(f"🔍 طلب البيانات المدمجة للجهاز: {device_id}")

        # استخدام النظام المبسط
        from services.device_tracker import device_tracker
        devices_data = await device_tracker.get_devices_from_database()

        # البحث عن الجهاز المطلوب
        device_data = None
        for device in devices_data.get('devices', []):
            if device.get('device_id') == device_id:
                device_data = device
                break

        if not device_data:
            from fastapi import HTTPException, status
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"لم يتم العثور على الجهاز: {device_id}"
            )

        result = {
            'success': True,
            'device_data': device_data,
            'data_source': 'database_only',
            'data_sources': ['database']
        }

        if not result['success']:
            logger.warning(f"⚠️ فشل في الحصول على البيانات المدمجة للجهاز: {device_id}")
            return {
                "success": False,
                "message": "فشل في الحصول على البيانات المدمجة",
                "error": result.get('error')
            }

        logger.info(f"✅ تم الحصول على البيانات المدمجة للجهاز: {device_id}")
        logger.debug(f"📊 مصادر البيانات: {result['data_sources']}")

        return {
            "success": True,
            "message": "تم الحصول على البيانات المدمجة بنجاح",
            "device_id": device_id,
            "data_sources": result['data_sources'],
            "live_data": result.get('live_data', {}),
            "static_data": result.get('static_data', {}),
            "integrated_data": result.get('integrated_data', {}),
            "has_live_data": bool(result.get('live_data', {})),
            "has_static_data": bool(result.get('static_data', {})),
            "is_fully_integrated": bool(result.get('live_data', {}) and result.get('static_data', {}))
        }

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على البيانات المدمجة للجهاز {device_id}: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"خطأ في الخادم: {str(e)}"
        }
